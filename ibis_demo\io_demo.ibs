[IBIS Ver] 4.2
[Comment char] |
[File Name] io_demo.ibs
[File Rev] 1.0
[Date] 2024-01-01
[Source] Generated for demonstration
[Notes] Example IBIS file for I/O model type
[Disclaimer] This is a demonstration file
[Copyright] Demo file

|***************************************************************************
|                            Component IO_DEMO
|***************************************************************************

[Component] IO_DEMO
[Manufacturer] Demo Company
[Package]
R_pkg               1.0m            0.8m            1.2m
L_pkg               1.0n            0.8n            1.2n  
C_pkg               0.2p            0.1p            0.3p

[Pin]  signal_name          model_name          R_pin    L_pin    C_pin
1      PAD                  IO_BUFFER        1.0m     1.0n     0.2p


[Model] IO_BUFFER
Model_type I/O
Polarity Non-Inverting
Enable Active-High
Vinl = 0.8V
Vinh = 2.0V
Vmeas = 1.5V
Cref = 50.0p
Rref = 50.0
Vref = 0

C_comp              4.0p            4.5p            5.0p

[Voltage Range]     5.0V            0.0V
[Temperature Range] 25C

[Pulldown]
|  voltage   I(typ)    I(min)    I(max)
-1.0V      -6.0m     -5.0m     -8.0m
0.0V       0.0A      0.0A      0.0A
2.5V       20.0m     15.0m     25.0m
5.0V       40.0m     30.0m     50.0m

[Pullup]
|  voltage   I(typ)    I(min)    I(max)
-1.0V      40.0m     30.0m     50.0m
0.0V       20.0m     15.0m     25.0m
2.5V       0.0A      0.0A      0.0A
5.0V       -20.0m    -15.0m    -25.0m

[GND Clamp]
|  voltage   I(typ)    I(min)    I(max)
-5.0V      -45.0m    -35.0m    -55.0m
-0.7V      -8.0m     -6.0m     -10.0m
0.0V       0.0A      0.0A      0.0A
5.0V       0.0A      0.0A      0.0A

[POWER Clamp]
|  voltage   I(typ)    I(min)    I(max)
-5.0V      45.0m     35.0m     55.0m
0.0V       0.0A      0.0A      0.0A
5.0V       0.0A      0.0A      0.0A

[Ramp]
|Variable            typ           min           max
dV/dt_r             1.5/2.0n      1.0/2.0n      2.0/2.0n
dV/dt_f             1.5/2.0n      1.0/2.0n      2.0/2.0n

[Rising Waveform]
R_fixture = 50.0
V_fixture = 2.5V
C_fixture = 50.0p
|  time      V(typ)    V(min)    V(max)
0.0n        0.0V      0.0V      0.0V
0.7n        0.4V      0.3V      0.5V
1.4n        1.5V      1.3V      1.7V
2.1n        3.0V      2.8V      3.2V
2.8n        4.3V      4.1V      4.5V
3.5n        5.0V      4.8V      5.2V

[Falling Waveform]
R_fixture = 50.0
V_fixture = 2.5V
C_fixture = 50.0p
|  time      V(typ)    V(min)    V(max)
0.0n        5.0V      4.8V      5.2V
0.7n        4.3V      4.1V      4.5V
1.4n        3.0V      2.8V      3.2V
2.1n        1.5V      1.3V      1.7V
2.8n        0.4V      0.3V      0.5V
3.5n        0.0V      0.0V      0.0V

[End]
