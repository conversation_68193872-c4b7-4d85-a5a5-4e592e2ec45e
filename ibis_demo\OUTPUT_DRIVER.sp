* SPICE Subcircuit generated from IBIS model
* Model: OUTPUT_DRIVER
* Model Type: output
* Generated by ibis2spice.py

* Output buffer - drives signals with push-pull output

.SUBCKT OUTPUT_DRIVER PAD VCC VSS PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 2.500000e-12F
C_comp PAD VSS 2.500000e-12F

* Voltage Range: [5.0, 0.0]

* Temperature Range: [25.0]

* Pulldown I-V Characteristic
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-1,-0.01) (0,0) (2.5,0.025) (5,0.05)

* Pullup I-V Characteristic
Gpu VCC PAD TABLE {V(VCC,PAD)} = (6,0.05) (5,0.025) (2.5,0) (0,0.01)

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,0.05) (0.7,0.01) (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,0.05) (5,0) (10,0)

* Ramp Information
* Rising edge dV/dt: [2.0, 1.5000000000000002e-09]
* Falling edge dV/dt: [2.0, 1.5000000000000002e-09]
* Load resistance: 50Ohm

* Rising Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Rising edge voltage source (for timing analysis)
Vrise_ref PAD_RISE 0 PWL(0 0 5.000000e-10s 0.2V 1.000000e-09s 1V 1.500000e-09s 2.5V
+ 2.000000e-09s 4V 2.500000e-09s 5V)

* Falling Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Falling edge voltage source (for timing analysis)
Vfall_ref PAD_FALL 0 PWL(0 5V 5.000000e-10s 4V 1.000000e-09s 2.5V 1.500000e-09s 1V
+ 2.000000e-09s 0.2V 2.500000e-09s 0)

.ENDS
