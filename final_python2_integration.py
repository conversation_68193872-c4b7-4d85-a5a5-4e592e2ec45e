#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终版本的Python2/3兼容IBIS转换器
修复了pybis模块导入问题
"""

import os
import sys
import subprocess
import tempfile
import shutil

class FinalIBISConverter:
    """最终版本的IBIS转换器"""
    
    def __init__(self, python_exe="python", pybis_dir=None):
        """
        初始化转换器
        
        Args:
            python_exe: Python可执行文件路径
            pybis_dir: pybis.py所在目录，默认为当前目录
        """
        self.python_exe = python_exe
        self.converter_script = "ibis2spice_simple copy.py"
        self.pybis_dir = pybis_dir or os.getcwd()
        self.temp_dir = None
        
        # 检查环境
        self._check_environment()
    
    def _check_environment(self):
        """检查Python环境"""
        try:
            # 检查Python
            proc = subprocess.Popen([self.python_exe, "--version"], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = proc.communicate()
            
            if proc.returncode == 0:
                version_info = self._safe_decode(stdout) or self._safe_decode(stderr)
                print("Python环境检查通过: {}".format(version_info.strip()))
            else:
                raise Exception("Python不可用")
            
            # 检查转换脚本
            if not os.path.exists(self.converter_script):
                raise Exception("转换脚本不存在: {}".format(self.converter_script))
            print("转换脚本检查通过")
            
            # 检查pybis.py
            pybis_path = os.path.join(self.pybis_dir, "pybis.py")
            if not os.path.exists(pybis_path):
                raise Exception("pybis.py不存在: {}".format(pybis_path))
            print("pybis.py检查通过: {}".format(pybis_path))
            
        except Exception as e:
            raise Exception("环境检查失败: {}".format(str(e)))
    
    def _safe_decode(self, data):
        """安全的字符串解码"""
        if data is None:
            return ""
        
        if isinstance(data, str):
            return data
        
        if hasattr(data, 'decode'):
            try:
                return data.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return data.decode('gbk')
                except UnicodeDecodeError:
                    return data.decode('latin-1', errors='ignore')
        
        return str(data)
    
    def _create_temp_dir(self):
        """创建临时目录"""
        if not self.temp_dir:
            self.temp_dir = tempfile.mkdtemp()
        return self.temp_dir
    
    def _cleanup_temp_dir(self):
        """清理临时目录"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None
            except:
                pass
    
    def convert(self, ibis_file, model_name, output_file):
        """转换IBIS模型到SPICE"""
        try:
            if not os.path.exists(ibis_file):
                return {
                    'success': False,
                    'message': "IBIS文件不存在: {}".format(ibis_file),
                    'output_file': None
                }
            
            cmd = [
                self.python_exe,
                self.converter_script,
                ibis_file,
                model_name,
                output_file
            ]
            
            print("执行转换: {} -> {}".format(model_name, output_file))
            
            # 设置工作目录为pybis所在目录
            proc = subprocess.Popen(cmd, 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE,
                                  cwd=self.pybis_dir)
            stdout, stderr = proc.communicate()
            
            stdout = self._safe_decode(stdout)
            stderr = self._safe_decode(stderr)
            
            if proc.returncode == 0:
                return {
                    'success': True,
                    'message': "转换成功\n{}".format(stdout),
                    'output_file': output_file
                }
            else:
                return {
                    'success': False,
                    'message': "转换失败\nSTDOUT: {}\nSTDERR: {}".format(stdout, stderr),
                    'output_file': None
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': "转换异常: {}".format(str(e)),
                'output_file': None
            }
    
    def get_models(self, ibis_file):
        """获取IBIS文件中的模型列表"""
        temp_script_path = None
        try:
            temp_dir = self._create_temp_dir()
            
            # 创建临时Python脚本，添加pybis路径
            temp_script_content = u'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
import sys
import os

# 添加pybis路径
pybis_dir = r"{}"
sys.path.insert(0, pybis_dir)

try:
    from pybis import IBSParser
    
    ibis_file = sys.argv[1]
    parser = IBSParser()
    
    with open(ibis_file, 'r') as f:
        root = parser.parse(f)
    
    models = []
    if 'Model' in root:
        models = list(root['Model'].keys())
    
    # 输出模型列表
    for model in models:
        print(model)
        
except Exception as e:
    print("ERROR: " + str(e))
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''.format(self.pybis_dir.replace('\\', '\\\\'))
            
            # 写入临时文件
            temp_script_path = os.path.join(temp_dir, "get_models.py")
            with open(temp_script_path, 'w', encoding='utf-8') as f:
                f.write(temp_script_content)
            
            # 执行脚本
            cmd = [self.python_exe, temp_script_path, ibis_file]
            proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = proc.communicate()
            
            stdout = self._safe_decode(stdout)
            stderr = self._safe_decode(stderr)
            
            if proc.returncode != 0:
                print("获取模型列表失败:")
                print("STDOUT: {}".format(stdout))
                print("STDERR: {}".format(stderr))
                return []
            
            # 解析输出
            lines = stdout.strip().split('\n')
            models = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith("ERROR:"):
                    models.append(line)
            
            return models
                
        except Exception as e:
            print("获取模型列表异常: {}".format(str(e)))
            return []
    
    def get_model_info(self, ibis_file, model_name):
        """获取模型基本信息"""
        temp_script_path = None
        try:
            temp_dir = self._create_temp_dir()
            
            temp_script_content = u'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
import sys
import json
import os

# 添加pybis路径
pybis_dir = r"{}"
sys.path.insert(0, pybis_dir)

try:
    from pybis import IBSParser
    
    ibis_file = sys.argv[1]
    model_name = sys.argv[2]
    
    parser = IBSParser()
    with open(ibis_file, 'r') as f:
        root = parser.parse(f)
    
    if 'Model' not in root or model_name not in root['Model']:
        result = {{'success': False, 'message': 'Model not found'}}
    else:
        model = root['Model'][model_name]
        result = {{
            'success': True,
            'model_name': model_name,
            'model_type': model.get('Model_type', 'Unknown'),
            'polarity': model.get('Polarity', 'Non-Inverting'),
            'c_comp': str(model.get('C_comp', [])),
            'has_pullup': 'Pullup' in model,
            'has_pulldown': 'Pulldown' in model,
            'has_gnd_clamp': 'GND_clamp' in model,
            'has_power_clamp': 'POWER_clamp' in model,
            'has_rising_waveform': 'Rising_waveform' in model,
            'has_falling_waveform': 'Falling_waveform' in model
        }}
    
    print(json.dumps(result))
        
except Exception as e:
    result = {{'success': False, 'message': str(e)}}
    print(json.dumps(result))
'''.format(self.pybis_dir.replace('\\', '\\\\'))
            
            temp_script_path = os.path.join(temp_dir, "get_model_info.py")
            with open(temp_script_path, 'w', encoding='utf-8') as f:
                f.write(temp_script_content)
            
            cmd = [self.python_exe, temp_script_path, ibis_file, model_name]
            proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = proc.communicate()
            
            stdout = self._safe_decode(stdout)
            
            if proc.returncode != 0:
                return {'success': False, 'message': 'Script execution failed'}
            
            try:
                import json
                result = json.loads(stdout.strip())
                return result
            except ValueError:
                return {'success': False, 'message': 'Invalid JSON output'}
                
        except Exception as e:
            return {'success': False, 'message': str(e)}
    
    def __del__(self):
        """析构函数，清理临时文件"""
        self._cleanup_temp_dir()


# 简化的使用函数
def quick_convert(ibis_file, model_name, output_file, python_exe="python"):
    """快速转换函数"""
    converter = FinalIBISConverter(python_exe)
    result = converter.convert(ibis_file, model_name, output_file)
    return result['success']


def quick_get_models(ibis_file, python_exe="python"):
    """快速获取模型列表"""
    converter = FinalIBISConverter(python_exe)
    return converter.get_models(ibis_file)


# 使用示例
def demo_usage():
    """使用示例"""
    print("=== 最终版本IBIS转换器示例 ===")
    
    # 测试文件
    ibis_file = r"D:\codes\Pybis\pybis-master\ibis_demo\input_demo.ibs"
    
    if not os.path.exists(ibis_file):
        print("测试文件不存在: {}".format(ibis_file))
        return
    
    try:
        # 创建转换器
        converter = FinalIBISConverter(python_exe="python")
        
        # 获取模型列表
        print("\n1. 获取模型列表:")
        models = converter.get_models(ibis_file)
        if models:
            print("找到 {} 个模型:".format(len(models)))
            for i, model in enumerate(models):
                print("  {}: {}".format(i+1, model))
        else:
            print("未找到模型")
            return
        
        # 获取第一个模型的信息
        if models:
            print("\n2. 获取模型信息:")
            model_info = converter.get_model_info(ibis_file, models[0])
            if model_info['success']:
                print("模型名称: {}".format(model_info['model_name']))
                print("模型类型: {}".format(model_info['model_type']))
                print("极性: {}".format(model_info['polarity']))
                print("C_comp: {}".format(model_info['c_comp']))
                print("特性:")
                features = ['has_pullup', 'has_pulldown', 'has_gnd_clamp', 'has_power_clamp', 
                           'has_rising_waveform', 'has_falling_waveform']
                for feature in features:
                    status = "✓" if model_info.get(feature, False) else "✗"
                    print("  {} {}".format(status, feature.replace('has_', '').replace('_', ' ').title()))
            else:
                print("获取模型信息失败: {}".format(model_info['message']))
        
        # 转换第一个模型
        if models:
            print("\n3. 转换模型:")
            output_file = "{}.sp".format(models[0])
            result = converter.convert(ibis_file, models[0], output_file)
            if result['success']:
                print("转换成功: {}".format(output_file))
                
                # 显示生成的SPICE文件内容（前几行）
                if os.path.exists(output_file):
                    print("\n生成的SPICE文件内容（前10行）:")
                    with open(output_file, 'r') as f:
                        lines = f.readlines()
                        for i, line in enumerate(lines[:10]):
                            print("  {}: {}".format(i+1, line.rstrip()))
                        if len(lines) > 10:
                            print("  ... (共{}行)".format(len(lines)))
            else:
                print("转换失败: {}".format(result['message']))
        
        # 批量转换示例
        if len(models) > 1:
            print("\n4. 批量转换示例:")
            for model in models[:3]:  # 只转换前3个
                output_file = "batch_{}.sp".format(model)
                result = converter.convert(ibis_file, model, output_file)
                status = "✓" if result['success'] else "✗"
                print("  {} {} -> {}".format(status, model, output_file))
        
    except Exception as e:
        print("示例执行失败: {}".format(str(e)))
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    demo_usage()
