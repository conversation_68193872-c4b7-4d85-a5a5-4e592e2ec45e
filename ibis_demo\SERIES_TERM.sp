* SPICE Subcircuit generated from IBIS model
* Model: SERIES_TERM
* Model Type: series
* Generated by ibis2spice.py

* Series termination buffer - source termination

.SUBCKT SERIES_TERM PAD VCC VSS PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 1.000000e-12F
C_comp PAD VSS 1.000000e-12F

* Voltage Range: [5.0, 0.0]

* Temperature Range: [25.0]

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,0.03) (0.7,0.003) (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,0.03) (5,0) (10,0)

.ENDS
