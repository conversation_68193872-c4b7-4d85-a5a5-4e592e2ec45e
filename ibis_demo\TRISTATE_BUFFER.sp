* SPICE Subcircuit generated from IBIS model
* Model: TRISTATE_BUFFER
* Model Type: 3-state
* Generated by ibis2spice.py

* Three-state buffer - output can be disabled (Hi-Z)

.SUBCKT TRISTATE_BUFFER PAD VCC VSS ENABLE PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* ENABLE   - Enable signal (for 3-state/I/O models)
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 3.000000e-12F
C_comp PAD VSS 3.000000e-12F

* Voltage Range: [5.0, 0.0]

* Temperature Range: [25.0]

* Pulldown I-V Characteristic
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-1,-0.008) (0,0) (2.5,0.025) (5,0.05)

* Pullup I-V Characteristic
Gpu VCC PAD TABLE {V(VCC,PAD)} = (6,0.05) (5,0.025) (2.5,0) (0,0.025)

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,0.05) (0.7,0.01) (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,0.05) (5,0) (10,0)

* Ramp Information
* Rising edge dV/dt: [1.8, 1.5000000000000002e-09]
* Falling edge dV/dt: [1.8, 1.5000000000000002e-09]
* Load resistance: 50Ohm

* Rising Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Rising edge voltage source (for timing analysis)
Vrise_ref PAD_RISE 0 PWL(0 0 6.000000e-10s 0.3V 1.200000e-09s 1.2V 1.800000e-09s 2.8V
+ 2.400000e-09s 4.2V 3.000000e-09s 5V)

* Falling Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Falling edge voltage source (for timing analysis)
Vfall_ref PAD_FALL 0 PWL(0 5V 6.000000e-10s 4.2V 1.200000e-09s 2.8V 1.800000e-09s 1.2V
+ 2.400000e-09s 0.3V 3.000000e-09s 0)

.ENDS
