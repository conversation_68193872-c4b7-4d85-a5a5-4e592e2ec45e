* SPICE Subcircuit generated from IBIS model
* Model: IO_OPEN_DRAIN
* Model Type: i/o_open_sink
* Generated by ibis2spice.py

* Unknown model type: i/o_open_sink

.SUBCKT IO_OPEN_DRAIN PAD VCC VSS ENABLE PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* ENABLE   - Enable signal (for 3-state/I/O models)
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 3.000000e-12F
C_comp PAD VSS 3.000000e-12F

* Voltage Range: [5.0, 0.0]

* Temperature Range: [25.0]

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,0.035) (0.7,0.004) (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,0.035) (5,0) (10,0)

* Ramp Information
* Rising edge dV/dt: [1.2, 2.5e-09]
* Falling edge dV/dt: [2.0, 1.5000000000000002e-09]
* Load resistance: 50Ohm

* Rising Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Rising edge voltage source (for timing analysis)
Vrise_ref PAD_RISE 0 PWL(0 0 1.200000e-09s 0.6V 2.400000e-09s 2.2V 3.600000e-09s 3.8V
+ 4.800000e-09s 4.7V 6.000000e-09s 5V)

* Falling Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Falling edge voltage source (for timing analysis)
Vfall_ref PAD_FALL 0 PWL(0 5V 6.000000e-10s 3.8V 1.200000e-09s 2.2V 1.800000e-09s 0.9V
+ 2.400000e-09s 0.3V 3.000000e-09s 0)

.ENDS
