[IBIS Ver] 4.2
[Comment char] |
[File Name] input_diff_demo.ibs
[File Rev] 1.0
[Date] 2024-01-01
[Source] Generated for demonstration
[Notes] Example IBIS file for Input_diff model type
[Disclaimer] This is a demonstration file
[Copyright] Demo file

|***************************************************************************
|                            Component INPUT_DIFF_DEMO
|***************************************************************************

[Component] INPUT_DIFF_DEMO
[Manufacturer] Demo Company
[Package]
R_pkg               1.0m            0.8m            1.2m
L_pkg               1.0n            0.8n            1.2n
C_pkg               0.2p            0.1p            0.3p

[Pin]  signal_name          model_name          R_pin    L_pin    C_pin
1      PAD_P                DIFF_INPUT          1.0m     1.0n     0.2p
2      PAD_N                DIFF_INPUT          1.0m     1.0n     0.2p

[Diff_Pin]   inv_pin    vdiff    tdelay_typ tdelay_min tdelay_max
1       2       0.2      0        NA         NA


[Model] DIFF_INPUT
Model_type Input_ECL
Polarity Non-Inverting
Vinl = 0.8V
Vinh = 2.0V
Vmeas = 1.5V
Cref = 50.0p
Rref = 50.0
Vref = 0

C_comp              1.5p            2.0p            2.5p

[Voltage Range]     3.3V            0.0V
[Temperature Range] 25C

[GND Clamp]
|  voltage   I(typ)    I(min)    I(max)
-2.0V      -20.0m    -15.0m    -25.0m
-0.7V      -3.0m     -2.0m     -4.0m
0.0V       0.0A      0.0A      0.0A
3.3V       0.0A      0.0A      0.0A

[POWER Clamp]
|  voltage   I(typ)    I(min)    I(max)
-2.0V      20.0m     15.0m     25.0m
0.0V       0.0A      0.0A      0.0A
3.3V       0.0A      0.0A      0.0A

[End]
