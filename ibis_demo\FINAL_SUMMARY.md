# IBIS 模型类型完整示例和转换结果

## 🎉 转换成功率: 100% (10/10)

本文档展示了 ibis2spice2.7.py 支持的所有 IBIS 模型类型的完整示例，包括正确的 IBIS 格式和对应的 SPICE 转换结果。

## 📋 支持的模型类型总览

| 序号 | 模型类型 | IBIS 文件 | SPICE 文件 | 特殊特性 | 状态 |
|------|----------|-----------|------------|----------|------|
| 1 | Input | input_demo.ibs | INPUT_BUFFER.sp | 仅接收 | ✅ |
| 2 | Output | output_demo.ibs | OUTPUT_DRIVER.sp | 推挽输出 + 波形 | ✅ |
| 3 | 3-state | tristate_demo.ibs | TRISTATE_BUFFER.sp | Enable 控制 | ✅ |
| 4 | I/O | io_demo.ibs | IO_BUFFER.sp | 双向 + Enable | ✅ |
| 5 | Open_drain | open_drain_demo.ibs | OPEN_DRAIN.sp | 仅下拉 | ✅ |
| 6 | I/O_open_drain | io_open_drain_demo.ibs | IO_OPEN_DRAIN.sp | 双向开漏 | ✅ |
| 7 | Input_ECL | input_diff_demo.ibs | DIFF_INPUT.sp | 差分输入 | ✅ |
| 8 | Output_ECL | output_diff_demo.ibs | DIFF_OUTPUT.sp | 差分输出 | ✅ |
| 9 | Series | series_demo.ibs | SERIES_TERM.sp | 串联端接 | ✅ |
| 10 | Terminator | terminator_demo.ibs | PASSIVE_TERM.sp | 无源端接 | ✅ |

## 🔧 关键技术要点

### 1. 差分模型处理
- **问题**: 真正的 Input_diff/Output_diff 需要 External Model
- **解决方案**: 使用 Input_ECL/Output_ECL 替代
- **特性**: 保持差分引脚定义和 Diff_Pin 映射

### 2. 串联模型处理
- **问题**: Series 模型不能直接在 Pin 表中使用
- **解决方案**: Pin 表使用 DUMMY_MODEL，Series Pin Mapping 使用 SERIES_TERM
- **特性**: 正确的 R Series 参数和 Pin Mapping

### 3. SPICE 转换特性
- **I-V 建模**: TABLE 函数实现非线性特性
- **波形建模**: PWL 电压源实现时域特性
- **端口定义**: 根据模型类型自动生成正确端口
- **参数提取**: 自动提取 C_comp, Ramp 等参数

## 📊 各模型类型详细信息

### Input Buffer
```ibis
[Model] INPUT_BUFFER
Model_type Input
C_comp              3.5p            4.0p            4.5p
[GND Clamp] / [POWER Clamp]
```
**SPICE 端口**: PAD, VCC, VSS

### Output Driver  
```ibis
[Model] OUTPUT_DRIVER
Model_type Output
C_comp              2.5p            3.0p            3.5p
[Pulldown] / [Pullup] / [Ramp] / [Rising/Falling Waveform]
```
**SPICE 端口**: PAD, VCC, VSS, PAD_RISE, PAD_FALL

### 3-state Buffer
```ibis
[Model] TRISTATE_BUFFER
Model_type 3-state
Enable Active-High
C_comp              3.0p            3.5p            4.0p
[Pulldown] / [Pullup] / [Ramp] / [Rising/Falling Waveform]
```
**SPICE 端口**: PAD, VCC, VSS, ENABLE, PAD_RISE, PAD_FALL

### I/O Buffer
```ibis
[Model] IO_BUFFER
Model_type I/O
Enable Active-High
C_comp              4.0p            4.5p            5.0p
[Pulldown] / [Pullup] / [Ramp] / [Rising/Falling Waveform]
```
**SPICE 端口**: PAD, VCC, VSS, ENABLE, PAD_RISE, PAD_FALL

### Open Drain
```ibis
[Model] OPEN_DRAIN
Model_type Open_drain
C_comp              2.0p            2.5p            3.0p
[Pulldown] / [Ramp] / [Rising/Falling Waveform]
```
**SPICE 端口**: PAD, VCC, VSS, PAD_RISE, PAD_FALL

### I/O Open Drain
```ibis
[Model] IO_OPEN_DRAIN
Model_type I/O_open_drain
Enable Active-High
C_comp              3.0p            3.5p            4.0p
[Pulldown] / [Ramp] / [Rising/Falling Waveform]
```
**SPICE 端口**: PAD, VCC, VSS, ENABLE, PAD_RISE, PAD_FALL

### ECL Input (差分)
```ibis
[Pin]  signal_name          model_name          R_pin    L_pin    C_pin
1      PAD_P                DIFF_INPUT          1.0m     1.0n     0.2p
2      PAD_N                DIFF_INPUT          1.0m     1.0n     0.2p

[Diff_Pin]   inv_pin    vdiff    tdelay_typ tdelay_min tdelay_max
1       2       0.2      0        NA         NA

[Model] DIFF_INPUT
Model_type Input_ECL
C_comp              1.5p            2.0p            2.5p
[GND Clamp] / [POWER Clamp]
```
**SPICE 端口**: PAD, VCC, VSS

### ECL Output (差分)
```ibis
[Pin]  signal_name          model_name          R_pin    L_pin    C_pin
1      PAD_P                DIFF_OUTPUT         1.0m     1.0n     0.2p
2      PAD_N                DIFF_OUTPUT         1.0m     1.0n     0.2p

[Diff_Pin]   inv_pin    vdiff    tdelay_typ tdelay_min tdelay_max
1       2       0.0      0        NA         NA

[Model] DIFF_OUTPUT
Model_type Output_ECL
C_comp              2.0p            2.5p            3.0p
[Pulldown] / [Pullup] / [Ramp] / [Rising/Falling Waveform]
```
**SPICE 端口**: PAD, VCC, VSS, PAD_RISE, PAD_FALL

### Series Termination
```ibis
[Pin]  signal_name          model_name          R_pin    L_pin    C_pin
1      PAD_1                DUMMY_MODEL         1.0m     1.0n     0.2p
2      PAD_2                DUMMY_MODEL         1.0m     1.0n     0.2p

[Series Pin Mapping]  pin_2    model_name
1       2         SERIES_TERM

[Model] SERIES_TERM
Model_type Series
C_comp              1.0p            1.5p            2.0p
[R Series]          50              45              55
[GND Clamp] / [POWER Clamp]
```
**SPICE 端口**: PAD, VCC, VSS, PAD_RISE, PAD_FALL

### Passive Terminator
```ibis
[Model] PASSIVE_TERM
Model_type Terminator
C_comp              0.5p            1.0p            1.5p
[GND Clamp] / [POWER Clamp]
```
**SPICE 端口**: PAD, VCC, VSS

## 🚀 使用建议

1. **选择合适的模型类型**:
   - 接收器 → Input
   - 驱动器 → Output  
   - 总线接口 → 3-state 或 I/O
   - 开漏总线 → Open_drain 或 I/O_open_drain
   - 差分信号 → Input_ECL 或 Output_ECL
   - 源端端接 → Series
   - 负载端接 → Terminator

2. **SPICE 仿真设置**:
   - 确保正确的电源电压 (VCC)
   - 设置适当的负载条件
   - 三态模型需要 ENABLE 信号控制
   - 使用波形端口进行时序分析

3. **参数调整**:
   - 可选择 min/typ/max 值 (当前使用 typ)
   - 根据工艺和温度调整参数
   - 验证 I-V 特性曲线的合理性

## ✅ 验证结果

所有生成的 SPICE 文件都通过了以下验证：
- ✅ 正确的 `.SUBCKT` / `.ENDS` 格式
- ✅ 包含 C_comp 电容建模
- ✅ I-V 特性使用 TABLE 函数
- ✅ 波形数据使用 PWL 电压源
- ✅ 端口定义符合模型特性
- ✅ SPICE 语法正确

ibis2spice2.7.py 转换器已经成功支持了 IBIS 标准中的所有主要模型类型，为数字电路的信号完整性分析提供了完整的解决方案。
