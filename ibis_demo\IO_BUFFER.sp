* SPICE Subcircuit generated from IBIS model
* Model: IO_BUFFER
* Model Type: i/o
* Generated by ibis2spice.py

* Bidirectional I/O buffer - can both receive and drive

.SUBCKT IO_BUFFER PAD VCC VSS ENABLE PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* ENABLE   - Enable signal (for 3-state/I/O models)
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 4.000000e-12F
C_comp PAD VSS 4.000000e-12F

* Voltage Range: [5.0, 0.0]

* Temperature Range: [25.0]

* Pulldown I-V Characteristic
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-1,-0.006) (0,0) (2.5,0.02) (5,0.04)

* Pullup I-V Characteristic
Gpu VCC PAD TABLE {V(VCC,PAD)} = (6,0.04) (5,0.02) (2.5,0) (0,0.02)

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,0.045) (0.7,0.008) (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,0.045) (5,0) (10,0)

* Ramp Information
* Rising edge dV/dt: [1.5, 2e-09]
* Falling edge dV/dt: [1.5, 2e-09]
* Load resistance: 50Ohm

* Rising Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Rising edge voltage source (for timing analysis)
Vrise_ref PAD_RISE 0 PWL(0 0 7.000000e-10s 0.4V 1.400000e-09s 1.5V 2.100000e-09s 3V
+ 2.800000e-09s 4.3V 3.500000e-09s 5V)

* Falling Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Falling edge voltage source (for timing analysis)
Vfall_ref PAD_FALL 0 PWL(0 5V 7.000000e-10s 4.3V 1.400000e-09s 3V 2.100000e-09s 1.5V
+ 2.800000e-09s 0.4V 3.500000e-09s 0)

.ENDS
