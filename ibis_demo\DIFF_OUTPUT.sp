* SPICE Subcircuit generated from IBIS model
* Model: DIFF_OUTPUT
* Model Type: output_ecl
* Generated by ibis2spice.py

* Unknown model type: output_ecl

.SUBCKT DIFF_OUTPUT PAD VCC VSS PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 2.000000e-12F
C_comp PAD VSS 2.000000e-12F

* Voltage Range: [3.3, 0.0]

* Temperature Range: [25.0]

* Pulldown I-V Characteristic
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-1,-0.008) (0,0) (1.65,0.015) (3.3,0.03)

* Pullup I-V Characteristic
Gpu VCC PAD TABLE {V(VCC,PAD)} = (6,0.03) (5,0.015) (3.35,0) (1.7,0.015)

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (2,0.025) (0.7,0.004) (0,0) (3.3,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (3,0.025) (5,0) (8.3,0)

* Ramp Information
* Rising edge dV/dt: [1.0, 1e-09]
* Falling edge dV/dt: [1.0, 1e-09]
* Load resistance: 50Ohm

* Rising Waveform V-T Characteristic
* R_fixture = 100Ohm
* V_fixture = 1.65V
* C_fixture = 2.500000e-11F
* Rising edge voltage source (for timing analysis)
Vrise_ref PAD_RISE 0 PWL(0 0 3.000000e-10s 0.3V 6.000000e-10s 1V 9.000000e-10s 2V
+ 1.200000e-09s 2.8V 1.500000e-09s 3.3V)

* Falling Waveform V-T Characteristic
* R_fixture = 100Ohm
* V_fixture = 1.65V
* C_fixture = 2.500000e-11F
* Falling edge voltage source (for timing analysis)
Vfall_ref PAD_FALL 0 PWL(0 3.3V 3.000000e-10s 2.8V 6.000000e-10s 2V 9.000000e-10s 1V
+ 1.200000e-09s 0.3V 1.500000e-09s 0)

.ENDS
