# IBIS 模型类型示例和 SPICE 转换总结

## 支持的模型类型概览

ibis2spice2.7.py 支持以下 IBIS 模型类型的转换：

| 模型类型 | Enable | Waveforms | Pull U/D | Differential | 转换状态 |
|---------|--------|-----------|----------|--------------|----------|
| Input | ❌ | ❌ | ❌ | ❌ | ✅ 成功 |
| Output | ❌ | ✅ | ✅ | ❌ | ✅ 成功 |
| 3-state | ✅ | ✅ | ✅ | ❌ | ✅ 成功 |
| I/O | ✅ | ✅ | ✅ | ❌ | ✅ 成功 |
| Open_drain | ❌ | ✅ | ❌ | ❌ | ✅ 成功 |
| I/O_open_drain | ✅ | ✅ | ❌ | ❌ | ✅ 成功 |
| Input_diff | ❌ | ❌ | ❌ | ✅ | ✅ 成功* |
| Output_diff | ❌ | ✅ | ✅ | ✅ | ✅ 成功* |
| Series | ❌ | ✅ | ✅ | ❌ | ❌ 失败 |
| Terminator | ❌ | ❌ | ❌ | ❌ | ✅ 成功 |

*注：差分模型当前简化为单端模型处理

## 详细示例

### 1. Input Buffer 示例

**IBIS 关键部分**:
```ibis
[Model] INPUT_BUFFER
Model_type Input
C_comp              3.5p            4.0p            4.5p
[GND Clamp]
-5.0V      -50.0m    -40.0m    -60.0m
0.0V       0.0A      0.0A      0.0A
[POWER Clamp]
-5.0V      50.0m     40.0m     60.0m
0.0V       0.0A      0.0A      0.0A
```

**对应 SPICE**:
```spice
.SUBCKT INPUT_BUFFER PAD VCC VSS
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground

C_comp PAD VSS 3.500000e-12F
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,0.05) (0.7,0.01) (0,0) (5,0)
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,0.05) (5,0) (10,0)
.ENDS
```

### 2. Output Driver 示例

**IBIS 关键部分**:
```ibis
[Model] OUTPUT_DRIVER
Model_type Output
C_comp              2.5p            3.0p            3.5p
[Pulldown]
0.0V       0.0A      0.0A      0.0A
2.5V       25.0m     20.0m     30.0m
5.0V       50.0m     40.0m     60.0m
[Pullup]
0.0V       25.0m     20.0m     30.0m
2.5V       0.0A      0.0A      0.0A
5.0V       -10.0m    -8.0m     -12.0m
[Ramp]
dV/dt_r             2.0/1.5n      1.5/1.5n      2.5/1.5n
dV/dt_f             2.0/1.5n      1.5/1.5n      2.5/1.5n
```

**对应 SPICE**:
```spice
.SUBCKT OUTPUT_DRIVER PAD VCC VSS PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

C_comp PAD VSS 2.500000e-12F
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-1,-0.01) (0,0) (2.5,0.025) (5,0.05)
Gpu VCC PAD TABLE {V(VCC,PAD)} = (6,0.05) (5,0.025) (2.5,0) (0,0.01)
Vrise_ref PAD_RISE 0 PWL(0 0 5.000000e-10s 0.2V 1.000000e-09s 1V ...)
Vfall_ref PAD_FALL 0 PWL(0 5V 5.000000e-10s 4V 1.000000e-09s 2.5V ...)
.ENDS
```

### 3. 3-state Buffer 示例

**IBIS 关键部分**:
```ibis
[Model] TRISTATE_BUFFER
Model_type 3-state
Enable Active-High
C_comp              3.0p            3.5p            4.0p
[Pulldown]
0.0V       0.0A      0.0A      0.0A
2.5V       25.0m     20.0m     30.0m
[Pullup]
0.0V       25.0m     20.0m     30.0m
2.5V       0.0A      0.0A      0.0A
```

**对应 SPICE**:
```spice
.SUBCKT TRISTATE_BUFFER PAD VCC VSS ENABLE PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* ENABLE   - Enable signal (for 3-state/I/O models)
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

C_comp PAD VSS 3.000000e-12F
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-1,-0.008) (0,0) (2.5,0.025) (5,0.05)
Gpu VCC PAD TABLE {V(VCC,PAD)} = (6,0.05) (5,0.025) (2.5,0) (0,0.025)
.ENDS
```

### 4. Open Drain 示例

**IBIS 关键部分**:
```ibis
[Model] OPEN_DRAIN
Model_type Open_drain
C_comp              2.0p            2.5p            3.0p
[Pulldown]
0.0V       0.0A      0.0A      0.0A
2.5V       30.0m     25.0m     35.0m
5.0V       60.0m     50.0m     70.0m
```

**对应 SPICE**:
```spice
.SUBCKT OPEN_DRAIN PAD VCC VSS PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

C_comp PAD VSS 2.500000e-12F
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-1,-0.005) (0,0) (2.5,0.03) (5,0.06)
* 注意：开漏模型没有 Pullup
.ENDS
```

## 端口定义规则

### 基本端口 (所有模型)
- **PAD**: I/O 焊盘连接
- **VCC**: 电源供应
- **VSS**: 地

### 控制端口 (三态和双向模型)
- **ENABLE**: 使能信号控制

### 波形端口 (有输出驱动的模型)
- **PAD_RISE**: 上升沿波形参考
- **PAD_FALL**: 下降沿波形参考

### 差分端口 (差分模型)
- **PAD_P**: 正差分焊盘
- **PAD_N**: 负差分焊盘
- **PAD_P_RISE/PAD_P_FALL**: 正端波形参考
- **PAD_N_RISE/PAD_N_FALL**: 负端波形参考

## SPICE 建模技术

### I-V 特性建模
使用 TABLE 函数实现非线性 I-V 关系：
```spice
Gpd PAD VSS TABLE {V(PAD,VSS)} = (v1,i1) (v2,i2) (v3,i3) ...
```

### 波形建模
使用 PWL (Piecewise Linear) 电压源：
```spice
Vrise_ref PAD_RISE 0 PWL(t1 v1 t2 v2 t3 v3 ...)
```

### 寄生参数
直接建模输入/输出电容：
```spice
C_comp PAD VSS capacitance_value
```

## 使用建议

1. **Input 模型**: 适用于接收器、比较器、时钟输入
2. **Output 模型**: 适用于标准逻辑门、时钟驱动器
3. **3-state 模型**: 适用于总线驱动器、存储器接口
4. **I/O 模型**: 适用于双向引脚、GPIO
5. **Open drain**: 适用于 I2C、1-wire、线或总线
6. **Terminator**: 适用于无源端接元件

## 仿真注意事项

1. **电源电压**: 确保 VCC 设置为正确的供电电压
2. **负载条件**: 根据实际应用设置适当的负载
3. **使能信号**: 三态模型需要正确的使能信号控制
4. **温度和工艺**: 当前使用典型值，可根据需要调整为最小/最大值
