* SPICE Subcircuit generated from IBIS model
* Model: OPEN_DRAIN
* Model Type: open_sink
* Generated by ibis2spice.py

* Unknown model type: open_sink

.SUBCKT OPEN_DRAIN PAD VCC VSS PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 2.000000e-12F
C_comp PAD VSS 2.000000e-12F

* Voltage Range: [5.0, 0.0]

* Temperature Range: [25.0]

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,0.04) (0.7,0.005) (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,0.04) (5,0) (10,0)

* Ramp Information
* Rising edge dV/dt: [1.0, 2e-09]
* Falling edge dV/dt: [2.5, 1e-09]
* Load resistance: 50Ohm

* Rising Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Rising edge voltage source (for timing analysis)
Vrise_ref PAD_RISE 0 PWL(0 0 1.000000e-09s 0.5V 2.000000e-09s 2V 3.000000e-09s 3.5V
+ 4.000000e-09s 4.5V 5.000000e-09s 5V)

* Falling Waveform V-T Characteristic
* R_fixture = 50Ohm
* V_fixture = 2.5V
* C_fixture = 5.000000e-11F
* Falling edge voltage source (for timing analysis)
Vfall_ref PAD_FALL 0 PWL(0 5V 4.000000e-10s 3.5V 8.000000e-10s 2V 1.200000e-09s 0.8V
+ 1.600000e-09s 0.2V 2.000000e-09s 0)

.ENDS
