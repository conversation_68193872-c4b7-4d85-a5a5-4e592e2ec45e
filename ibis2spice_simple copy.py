#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IBIS to SPICE Converter (Simplified Version - No Differential Support)
将IBIS模型转换为SPICE子电路 (简化版本 - 不支持差分模型)

使用pybis.py解析.ibs文件，提取模型数据，
并将其转换为SPICE子电路格式，输出到.sp文件中。

此版本专注于基本的单端模型类型，不支持差分模型以避免复杂性。
"""

import sys
import os
import math
from pybis import IBSParser

def format_spice_value(value, unit=''):
    """
    将数值格式化为SPICE格式
    """
    if value is None or value == 'None':
        return '0'
    
    try:
        val = float(value)
        if val == 0:
            return '0'
        
        # 使用科学记数法或工程记数法
        if abs(val) >= 1e-3 and abs(val) < 1e3:
            return "{:.6g}{}".format(val, unit)
        else:
            return "{:.6e}{}".format(val, unit)
    except:
        return str(value)

def extract_iv_data(iv_range, type_num):
    """
    从IBIS Range对象中提取I-V数据
    返回(voltage_list, current_list)
    """
    if iv_range is None or iv_range[type_num] is None or len(iv_range) == 0:
        return [], []

    # 获取典型值数据 (index 0)
    typ_data = iv_range[type_num]
    if typ_data is None:
        return [], []

    # 解析字符串格式的数据
    data_str = str(typ_data)
    lines = data_str.strip().split('\n')
    
    voltages = []
    currents = []
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('|') or line.startswith('#'):
            continue
        
        # 分割电压和电流值
        parts = line.split()
        if len(parts) >= 2:
            try:
                voltage = float(parts[0].replace('V', ''))
                current = float(parts[1].replace('A', '').replace('m', 'e-3').replace('u', 'e-6').replace('n', 'e-9').replace('p', 'e-12'))
                voltages.append(voltage)
                currents.append(current)
            except ValueError:
                continue
    
    return voltages, currents

def generate_table_function(voltages, currents, name):
    """
    生成SPICE TABLE函数
    """
    if not voltages or not currents or len(voltages) != len(currents):
        return ""
    
    # 构建TABLE函数
    table_pairs = []
    for v, i in zip(voltages, currents):
        table_pairs.append("({},{})".format(format_spice_value(v), format_spice_value(i)))
    
    table_str = " ".join(table_pairs)
    return "TABLE {{{}}} = {}".format(name, table_str)

def extract_waveform_data(waveform, type_num):
    """
    从IBIS波形数据中提取时间-电压对
    """
    if waveform is None or len(waveform) == 0:
        return [], []
    
    # 获取典型值数据
    typ_data = waveform[type_num] if len(waveform) > type_num else None
    if typ_data is None:
        return [], []
    
    data_str = str(typ_data)
    lines = data_str.strip().split('\n')
    
    times = []
    voltages = []
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('|') or line.startswith('#'):
            continue
        
        parts = line.split()
        if len(parts) >= 2:
            try:
                time = float(parts[0].replace('s', '').replace('n', 'e-9').replace('p', 'e-12'))
                voltage = float(parts[1].replace('V', ''))
                times.append(time)
                voltages.append(voltage)
            except ValueError:
                continue
    
    return times, voltages

def generate_pwl_waveform(times, voltages, source_name):
    """
    生成PWL波形源
    """
    if not times or not voltages or len(times) != len(voltages):
        return ""
    
    pwl_pairs = []
    for t, v in zip(times, voltages):
        pwl_pairs.append("{}s {}V".format(format_spice_value(t), format_spice_value(v)))
    
    pwl_str = " ".join(pwl_pairs)
    return "{} 0 PWL({})".format(source_name, pwl_str)

def determine_model_features(model_type):
    """
    根据IBIS模型类型确定需要的功能特性 (简化版本 - 不支持差分)
    返回: (needs_enable, needs_waveforms, needs_pullup_pulldown)
    """
    # 标准化模型类型名称（去除大小写差异和特殊字符）
    model_type_norm = model_type.lower().replace('_', '').replace('-', '').replace('/', '')

    # 需要ENABLE信号的模型类型
    enable_types = {
        '3state', 'io', 'ioopendrain', 'ioopensink', 'ioopensource', 'seriesswitch'
    }

    # 需要波形数据的模型类型（有输出驱动能力的）
    waveform_types = {
        'output', '3state', 'io', 'opendrain', 'ioopendrain',
        'opensink', 'ioopensink', 'opensource', 'ioopensource', 'series', 'seriesswitch'
    }

    # 需要Pullup/Pulldown的模型类型（推挽输出）
    pullup_pulldown_types = {
        'output', '3state', 'io', 'series'
    }

    needs_enable = model_type_norm in enable_types
    needs_waveforms = model_type_norm in waveform_types
    needs_pullup_pulldown = model_type_norm in pullup_pulldown_types

    return needs_enable, needs_waveforms, needs_pullup_pulldown

def get_model_type_description(model_type):
    """
    获取模型类型的描述 (简化版本)
    """
    descriptions = {
        'Input': 'Input buffer - receives signals only',
        'Output': 'Output buffer - drives signals with push-pull output',
        'I/O': 'Bidirectional I/O buffer - can both receive and drive',
        '3-state': 'Three-state buffer - output can be disabled (Hi-Z)',
        'Open_drain': 'Open drain output - can only pull low',
        'I/O_open_drain': 'Bidirectional open drain I/O',
        'Open_sink': 'Open sink output - current sink only',
        'I/O_open_sink': 'Bidirectional open sink I/O',
        'Open_source': 'Open source output - current source only',
        'I/O_open_source': 'Bidirectional open source I/O',
        'Series': 'Series termination buffer - source termination',
        'Series_switch': 'Switchable series termination buffer',
        'Terminator': 'Passive termination element',
        # 添加小写版本支持
        'input': 'Input buffer - receives signals only',
        'output': 'Output buffer - drives signals with push-pull output',
        'i/o': 'Bidirectional I/O buffer - can both receive and drive',
        '3-state': 'Three-state buffer - output can be disabled (Hi-Z)',
        'open_drain': 'Open drain output - can only pull low',
        'i/o_open_drain': 'Bidirectional open drain I/O',
        'series': 'Series termination buffer - source termination',
        'terminator': 'Passive termination element'
    }
    
    return descriptions.get(model_type, "Single-ended I/O buffer")

def check_differential_model(model_type):
    """
    检查是否为差分模型类型，如果是则拒绝处理
    """
    model_type_norm = model_type.lower().replace('_', '').replace('-', '').replace('/', '')
    
    # 差分信号类型 (这些类型将被拒绝)
    differential_types = {
        'inputdiff', 'outputdiff', 'iodiff', '3statediff',
        'inputecl', 'outputecl', 'ioecl', '3stateecl'
    }
    
    return model_type_norm in differential_types

def convert_ibis_to_spice_simple(root, model_name, output_file, type_num):
    """
    将IBIS模型转换为SPICE子电路 (简化版本 - 不支持差分)
    """
    if 'Model' not in root or model_name not in root['Model']:
        print("Error: Model '{}' not found in IBIS file".format(model_name))
        return False

    model = root['Model'][model_name]

    print("Converting model '{}' to SPICE subcircuit...".format(model_name))

    # 检查模型类型
    model_type = model.get('Model_type', '3-state')  # 默认为3-state
    print("Model type: {}".format(model_type))

    # 检查是否为差分模型
    if check_differential_model(model_type):
        print("Error: Differential model type '{}' is not supported in this simplified version".format(model_type))
        print("Supported model types: Input, Output, 3-state, I/O, Open_drain, I/O_open_drain, Series, Terminator")
        return 1

    # 根据模型类型确定端口和功能
    needs_enable, needs_waveforms, needs_pullup_pulldown = determine_model_features(model_type)

    print("Features: Enable={}, Waveforms={}, Pullup/Pulldown={}".format(needs_enable, needs_waveforms, needs_pullup_pulldown))

    # 开始生成SPICE文件
    spice_content = []
    spice_content.append("* SPICE Subcircuit generated from IBIS model")
    spice_content.append("* Model: {}".format(model_name))
    spice_content.append("* Model Type: {}".format(model_type))
    spice_content.append("* Generated by ibis2spice_simple.py")
    spice_content.append("")

    # 添加模型类型说明
    model_description = get_model_type_description(model_type)
    if model_description:
        spice_content.append("* {}".format(model_description))
        spice_content.append("")

    # 生成单端信号端口定义
    ports = ["PAD", "VCC", "VSS"]
    port_comments = [
        "* PAD      - I/O pad connection",
        "* VCC      - Power supply",
        "* VSS      - Ground"
    ]

    # 根据模型特性添加额外端口
    if needs_enable:
        ports.append("ENABLE")
        port_comments.append("* ENABLE   - Enable signal (for 3-state/I/O models)")

    if needs_waveforms:
        ports.extend(["PAD_RISE", "PAD_FALL"])
        port_comments.extend([
            "* PAD_RISE - Rising waveform reference output",
            "* PAD_FALL - Falling waveform reference output"
        ])

    # 生成子电路定义
    spice_content.append(".SUBCKT {} {}".format(model_name, " ".join(ports)))
    spice_content.extend(port_comments)
    spice_content.append("")

    # 提取并添加C_comp
    c_comp = model.get('C_comp')
    if c_comp and len(c_comp) > type_num:
        c_value = format_spice_value(c_comp[type_num], 'F')
        spice_content.append("* Input/Output Capacitance: {}".format(c_value))
        spice_content.append("C_comp PAD VSS {}".format(c_value))
        spice_content.append("")

    # 添加电压范围信息
    voltage_range = model.get('Voltage_range')
    if voltage_range:
        spice_content.append("* Voltage Range: {}".format(voltage_range))
        spice_content.append("")

    # 添加温度范围信息
    temp_range = model.get('Temperature_range')
    if temp_range:
        spice_content.append("* Temperature Range: {}".format(temp_range))
        spice_content.append("")

    # 处理Pulldown特性
    if needs_pullup_pulldown and model.get('Pulldown'):
        voltages, currents = extract_iv_data(model['Pulldown'], type_num)
        if voltages and currents:
            spice_content.append("* Pulldown I-V Characteristic")
            table_func = generate_table_function(voltages, currents, "V(PAD,VSS)")
            spice_content.append("Gpd PAD VSS {}".format(table_func))
            spice_content.append("")

    # 处理Pullup特性
    if needs_pullup_pulldown and model.get('Pullup'):
        voltages, currents = extract_iv_data(model['Pullup'], type_num)
        if voltages and currents:
            spice_content.append("* Pullup I-V Characteristic")
            table_func = generate_table_function(voltages, currents, "V(VCC,PAD)")
            spice_content.append("Gpu VCC PAD {}".format(table_func))
            spice_content.append("")

    # 处理GND Clamp
    if model.get('GND_clamp'):
        voltages, currents = extract_iv_data(model['GND_clamp'], type_num)
        if voltages and currents:
            spice_content.append("* GND Clamp I-V Characteristic")
            table_func = generate_table_function(voltages, currents, "V(VSS,PAD)")
            spice_content.append("Gclamp_gnd VSS PAD {}".format(table_func))
            spice_content.append("")

    # 处理POWER Clamp
    if model.get('POWER_clamp'):
        voltages, currents = extract_iv_data(model['POWER_clamp'], type_num)
        if voltages and currents:
            spice_content.append("* POWER Clamp I-V Characteristic")
            table_func = generate_table_function(voltages, currents, "V(PAD,VCC)")
            spice_content.append("Gclamp_pwr PAD VCC {}".format(table_func))
            spice_content.append("")

    # 处理Ramp信息
    if needs_waveforms and model.get('Ramp'):
        ramp = model['Ramp']
        spice_content.append("* Ramp Information")
        if hasattr(ramp, 'dV_dt_r') and len(ramp.dV_dt_r) > type_num:
            spice_content.append("* Rising edge dV/dt: {}".format(ramp.dV_dt_r[type_num]))
        if hasattr(ramp, 'dV_dt_f') and len(ramp.dV_dt_f) > type_num:
            spice_content.append("* Falling edge dV/dt: {}".format(ramp.dV_dt_f[type_num]))
        if hasattr(ramp, 'R_load'):
            spice_content.append("* Load resistance: {}Ohm".format(ramp.R_load))
        spice_content.append("")

    # 处理Rising Waveform
    if needs_waveforms and model.get('Rising_waveform'):
        times, voltages = extract_waveform_data(model['Rising_waveform'], type_num)
        if times and voltages:
            spice_content.append("* Rising Waveform V-T Characteristic")
            # 添加测试条件信息
            rising_wf = model['Rising_waveform']
            if hasattr(rising_wf, 'R_fixture'):
                spice_content.append("* R_fixture = {}Ohm".format(rising_wf.R_fixture))
            if hasattr(rising_wf, 'V_fixture'):
                spice_content.append("* V_fixture = {}V".format(rising_wf.V_fixture))
            if hasattr(rising_wf, 'C_fixture'):
                spice_content.append("* C_fixture = {}F".format(format_spice_value(rising_wf.C_fixture)))
            
            spice_content.append("* Rising edge voltage source (for timing analysis)")
            pwl_source = generate_pwl_waveform(times, voltages, "Vrise_ref PAD_RISE")
            spice_content.append(pwl_source)
            spice_content.append("")

    # 处理Falling Waveform
    if needs_waveforms and model.get('Falling_waveform'):
        times, voltages = extract_waveform_data(model['Falling_waveform'], type_num)
        if times and voltages:
            spice_content.append("* Falling Waveform V-T Characteristic")
            # 添加测试条件信息
            falling_wf = model['Falling_waveform']
            if hasattr(falling_wf, 'R_fixture'):
                spice_content.append("* R_fixture = {}Ohm".format(falling_wf.R_fixture))
            if hasattr(falling_wf, 'V_fixture'):
                spice_content.append("* V_fixture = {}V".format(falling_wf.V_fixture))
            if hasattr(falling_wf, 'C_fixture'):
                spice_content.append("* C_fixture = {}F".format(format_spice_value(falling_wf.C_fixture)))
            
            spice_content.append("* Falling edge voltage source (for timing analysis)")
            pwl_source = generate_pwl_waveform(times, voltages, "Vfall_ref PAD_FALL")
            spice_content.append(pwl_source)
            spice_content.append("")

    # 结束子电路定义
    spice_content.append(".ENDS")
    spice_content.append("")

    # 写入SPICE文件
    try:
        with open(output_file, 'w') as f:
            f.write('\n'.join(spice_content))
        print("SPICE subcircuit successfully written to {}".format(output_file))
        return True
    except Exception as e:
        print("Error writing SPICE file: {}".format(e))
        return False

def main_process(ibis_file, model_name, output_file, type_num=0):
    """
    主处理函数
    """
    print("Parsing IBIS file: {}".format(ibis_file))

    # 解析IBIS文件
    try:
        parser = IBSParser()
        with open(ibis_file, 'r') as f:
            root = parser.parse(f)
        print("Successfully parsed IBIS file")
    except Exception as e:
        print("Error parsing IBIS file: {}".format(e))
        return 1

    # 转换为SPICE
    success = convert_ibis_to_spice_simple(root, model_name, output_file, type_num)

    if success:
        print("Conversion completed successfully!")
        print("Output file: {}".format(output_file))
        return 0
    else:
        print("Conversion failed!")
        return 1

if __name__ == "__main__":
    ibis_file = "ibis/at16245.ibs"
    model_name = "AT16245_OUT"
    output_file = "AT16245_OUT.sp"
    type_num = 0
    main_process(ibis_file, model_name, output_file, type_num)